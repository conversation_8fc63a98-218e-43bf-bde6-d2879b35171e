// 用户相关类型
export interface User {
  id: string;
  username: string;
  email: string;
  is_active: boolean;
  is_admin: boolean;
  created_at: string;
  updated_at: string;
  token_limit?: number | null; // Token使用限制，null表示无限制
  last_login?: string | null; // 最后登录时间，null表示从未登录
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
}

// 管理员创建用户请求类型
export interface AdminCreateUserRequest {
  username: string;
  email: string;
  password: string;
  is_active?: boolean;
  token_limit?: number | null;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  token?: string;
  user?: User;
}

// 学生信息相关类型
export interface StudentInfo {
  // 基本信息
  studentName: string;
  studentGender: '男' | '女';

  // 学业成绩与发展
  subjectStrengths: string;
  subjectWeaknesses: string;
  learningPotential: '潜力较大' | '学习能力稳定' | '需要进一步激发潜力';
  subjectInterest: '浓厚' | '一般' | '缺乏兴趣';

  // 课堂表现与参与
  classroomConcentration: '始终高度专注' | '大部分时间专注' | '有时分散注意力' | '容易受外界干扰';

  // 行为习惯与态度
  homeworkCompletion: '总是按时高质量' | '大部分按时完成' | '有时拖延或应付';
  learningProactiveness: '学习主动性强' | '完成要求任务' | '需要督促';
  disciplineCompliance: '严格遵守' | '基本遵守' | '需要加强';
  attitudeTowardsOthers: '有礼貌尊重他人' | '基本有礼貌' | '不够尊重';
  responsibility: '较强' | '能完成分配任务' | '有待提高';

  // 个性特长与潜能
  talentsAndInterests: string;
  classPosition: '班长' | '副班长' | '学习委员' | '体育委员' | '文艺委员' | '生活委员' | '纪律委员' | '宣传委员' | '组织委员' | '科代表' | '小组长' | '无职位';
  awards: string;

  // 教师期望与个性化建议
  overallAssessment: string;
  futureExpectations: string;
  improvementSuggestions: string;

  // 评语设置
  commentPerspective: '你' | '该生';
  commentTone: '温和亲切' | '严谨正式' | '鼓励激励' | '客观中性';
  wordCountRange: string;
}

// 自定义选项管理相关类型
export interface CustomOption {
  id: string;
  field_name: string;
  option_value: string;
  user_id: string;
  created_at: string;
}

export interface FieldOptions {
  [fieldName: string]: string[];
}

// 评语生成相关类型
export interface CommentGenerationRequest {
  studentInfo: StudentInfo;
}

export interface CommentGenerationResponse {
  success: boolean;
  message: string;
  comment?: string;
  tokensUsed?: number;
}

// Token使用记录类型
export interface TokenUsage {
  id: string;
  user_id: string;
  tokens_used: number;
  api_call_type: string;
  request_data?: any;
  response_data?: any;
  created_at: string;
}

// 用户Token统计类型
export interface UserTokenStats {
  user_id: string;
  username: string;
  total_tokens_used: number;
  token_limit: number | null;
  usage_percentage: number;
  recent_usage: TokenUsage[];
  daily_usage: { date: string; tokens: number }[];
}

// Token限制设置请求类型
export interface TokenLimitRequest {
  userId: string;
  tokenLimit: number | null; // null表示无限制
}

// 管理员统计类型
export interface AdminStats {
  totalUsers: number;
  activeUsers: number;
  totalTokensUsed: number;
  totalComments: number;
}

// 评语记录类型
export interface CommentRecord {
  id: string;
  user_id: string;
  student_name: string;
  student_info: StudentInfo;
  generated_comment: string;
  tokens_used: number;
  created_at: string;
}

// 历史评语查询请求类型
export interface CommentsQueryRequest {
  page?: number;
  limit?: number;
  studentName?: string;
  startDate?: string;
  endDate?: string;
}

// 历史评语查询响应类型
export interface CommentsQueryResponse {
  success: boolean;
  message: string;
  comments?: CommentRecord[];
  total?: number;
  page?: number;
  limit?: number;
  totalPages?: number;
}
