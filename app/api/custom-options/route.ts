import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { verifyToken } from '@/lib/auth';

// 获取用户的自定义选项
export async function GET(request: NextRequest) {
  try {
    // 验证token
    const authResult = await verifyToken(request);
    if (!authResult.success || !authResult.payload) {
      return NextResponse.json({
        success: false,
        message: '未授权访问'
      }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const fieldName = searchParams.get('fieldName');

    let query = supabaseAdmin
      .from('custom_options')
      .select('*')
      .eq('user_id', authResult.payload.userId)
      .order('created_at', { ascending: true });

    if (fieldName) {
      query = query.eq('field_name', fieldName);
    }

    const { data: options, error } = await query;

    if (error) {
      console.error('获取自定义选项失败:', error);
      return NextResponse.json({
        success: false,
        message: '获取选项失败'
      }, { status: 500 });
    }

    // 按字段名分组
    const groupedOptions: { [key: string]: string[] } = {};
    options?.forEach(option => {
      if (!groupedOptions[option.field_name]) {
        groupedOptions[option.field_name] = [];
      }
      groupedOptions[option.field_name].push(option.option_value);
    });

    return NextResponse.json({
      success: true,
      options: fieldName ? (groupedOptions[fieldName] || []) : groupedOptions
    });

  } catch (error) {
    console.error('获取自定义选项错误:', error);
    return NextResponse.json({
      success: false,
      message: '服务器错误'
    }, { status: 500 });
  }
}

// 添加自定义选项
export async function POST(request: NextRequest) {
  try {
    // 验证token
    const authResult = await verifyToken(request);
    if (!authResult.success || !authResult.payload) {
      return NextResponse.json({
        success: false,
        message: '未授权访问'
      }, { status: 401 });
    }

    const body = await request.json();
    const { fieldName, optionValue } = body;

    if (!fieldName || !optionValue) {
      return NextResponse.json({
        success: false,
        message: '字段名和选项值不能为空'
      }, { status: 400 });
    }

    // 检查是否已存在相同的选项
    const { data: existingOption } = await supabaseAdmin
      .from('custom_options')
      .select('id')
      .eq('user_id', authResult.payload.userId)
      .eq('field_name', fieldName)
      .eq('option_value', optionValue)
      .single();

    if (existingOption) {
      return NextResponse.json({
        success: false,
        message: '该选项已存在'
      }, { status: 400 });
    }

    // 添加新选项
    const { data, error } = await supabaseAdmin
      .from('custom_options')
      .insert({
        user_id: authResult.payload.userId,
        field_name: fieldName,
        option_value: optionValue
      })
      .select()
      .single();

    if (error) {
      console.error('添加自定义选项失败:', error);
      return NextResponse.json({
        success: false,
        message: '添加选项失败'
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: '选项添加成功',
      option: data
    });

  } catch (error) {
    console.error('添加自定义选项错误:', error);
    return NextResponse.json({
      success: false,
      message: '服务器错误'
    }, { status: 500 });
  }
}

// 删除自定义选项
export async function DELETE(request: NextRequest) {
  try {
    // 验证token
    const authResult = await verifyToken(request);
    if (!authResult.success || !authResult.payload) {
      return NextResponse.json({
        success: false,
        message: '未授权访问'
      }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const fieldName = searchParams.get('fieldName');
    const optionValue = searchParams.get('optionValue');

    if (!fieldName || !optionValue) {
      return NextResponse.json({
        success: false,
        message: '字段名和选项值不能为空'
      }, { status: 400 });
    }

    const { error } = await supabaseAdmin
      .from('custom_options')
      .delete()
      .eq('user_id', authResult.payload.userId)
      .eq('field_name', fieldName)
      .eq('option_value', optionValue);

    if (error) {
      console.error('删除自定义选项失败:', error);
      return NextResponse.json({
        success: false,
        message: '删除选项失败'
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: '选项删除成功'
    });

  } catch (error) {
    console.error('删除自定义选项错误:', error);
    return NextResponse.json({
      success: false,
      message: '服务器错误'
    }, { status: 500 });
  }
}
